<?php
require_once '../config/config.php';
requireLogin();

$page_title = 'Account Settings';
$site_name = getBankName();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    try {
        $db = getDB();
        $user_id = $_SESSION['user_id'];

        if ($action === 'update_profile') {
            $first_name = trim($_POST['first_name'] ?? '');
            $last_name = trim($_POST['last_name'] ?? '');
            $email = trim($_POST['email'] ?? '');
            $phone = trim($_POST['phone'] ?? '');
            $address = trim($_POST['address'] ?? '');
            $date_of_birth = $_POST['date_of_birth'] ?? '';
            $occupation = trim($_POST['occupation'] ?? '');
            $marital_status = $_POST['marital_status'] ?? '';
            $gender = $_POST['gender'] ?? '';

            // Validate required fields
            if (empty($first_name) || empty($last_name) || empty($email)) {
                throw new Exception('First name, last name, and email are required');
            }

            // Check if email is already taken by another user
            $email_check = $db->query("SELECT id FROM accounts WHERE email = ? AND id != ?", [$email, $user_id]);
            if ($email_check->num_rows > 0) {
                throw new Exception('Email address is already in use');
            }

            // Update profile
            $update_sql = "UPDATE accounts SET
                          first_name = ?, last_name = ?, email = ?, phone = ?,
                          address = ?, date_of_birth = ?, occupation = ?,
                          marital_status = ?, gender = ?, updated_at = NOW()
                          WHERE id = ?";

            $result = $db->query($update_sql, [
                $first_name, $last_name, $email, $phone, $address,
                $date_of_birth ?: null, $occupation, $marital_status, $gender, $user_id
            ]);

            if ($result) {
                // Update session data
                $_SESSION['first_name'] = $first_name;
                $_SESSION['last_name'] = $last_name;
                $_SESSION['email'] = $email;

                $success_message = 'Profile updated successfully';
            } else {
                throw new Exception('Failed to update profile');
            }

        } elseif ($action === 'change_password') {
            $current_password = $_POST['current_password'] ?? '';
            $new_password = $_POST['new_password'] ?? '';
            $confirm_password = $_POST['confirm_password'] ?? '';

            // Validate passwords
            if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
                throw new Exception('All password fields are required');
            }

            if ($new_password !== $confirm_password) {
                throw new Exception('New passwords do not match');
            }

            if (strlen($new_password) < 8) {
                throw new Exception('New password must be at least 8 characters long');
            }

            // Verify current password
            $user_result = $db->query("SELECT password FROM accounts WHERE id = ?", [$user_id]);
            $user_data = $user_result->fetch_assoc();

            if (!password_verify($current_password, $user_data['password'])) {
                throw new Exception('Current password is incorrect');
            }

            // Update password
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $result = $db->query("UPDATE accounts SET password = ?, updated_at = NOW() WHERE id = ?", [$hashed_password, $user_id]);

            if ($result) {
                $success_message = 'Password changed successfully';
            } else {
                throw new Exception('Failed to change password');
            }
        }

    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get user data
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    $user_sql = "SELECT * FROM accounts WHERE id = ?";
    $user_result = $db->query($user_sql, [$user_id]);
    $user_data = $user_result->fetch_assoc();

    // Get login attempts for security info
    $login_attempts_sql = "SELECT COUNT(*) as count FROM login_attempts
                          WHERE username = ? AND attempted_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)";
    $attempts_result = $db->query($login_attempts_sql, [$user_data['username']]);
    $recent_attempts = $attempts_result->fetch_assoc()['count'];

} catch (Exception $e) {
    error_log("Settings page error: " . $e->getMessage());
    $user_data = [];
    $recent_attempts = 0;
}

// Include header
require_once '../includes/dashboard/header.php';

// Include sidebar
require_once '../includes/dashboard/sidebar.php';
?>

<!-- Main Content -->
<div class="main-content">
    <!-- Top Bar -->
    <div class="top-bar">
        <h1>Account Settings</h1>
        <div class="top-bar-actions">
            <button class="btn-outline" onclick="window.location.reload()">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path fill-rule="evenodd" d="M8 3a5 5 0 104.546 2.914.5.5 0 00-.908-.417A4 4 0 118 4v1z"/>
                    <path d="M8 4.466V.534a.25.25 0 01.41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 018 4.466z"/>
                </svg>
                Refresh
            </button>
            <div style="width: 40px; height: 40px; border-radius: 50%; background: #6366f1; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                <?php echo strtoupper(substr($_SESSION['first_name'] ?? 'U', 0, 1)); ?>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (isset($success_message)): ?>
        <div class="alert alert-success" style="margin-bottom: 2rem; padding: 1rem; background: #d1fae5; border: 1px solid #10b981; border-radius: 8px; color: #065f46;">
            <strong>Success!</strong> <?php echo htmlspecialchars($success_message); ?>
        </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger" style="margin-bottom: 2rem; padding: 1rem; background: #fee2e2; border: 1px solid #ef4444; border-radius: 8px; color: #991b1b;">
            <strong>Error!</strong> <?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif; ?>

    <!-- Settings Grid -->
    <div class="dashboard-grid">
        <div class="main-section">
            <!-- Profile Information -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Profile Information</h3>
                </div>
                <div class="card-body" style="padding: 2rem;">
                    <form method="POST" id="profileForm">
                        <input type="hidden" name="action" value="update_profile">

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem; margin-bottom: 1.5rem;">
                            <div>
                                <label class="form-label">First Name *</label>
                                <input type="text" class="form-control" name="first_name" value="<?php echo htmlspecialchars($user_data['first_name'] ?? ''); ?>" required>
                            </div>
                            <div>
                                <label class="form-label">Last Name *</label>
                                <input type="text" class="form-control" name="last_name" value="<?php echo htmlspecialchars($user_data['last_name'] ?? ''); ?>" required>
                            </div>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem; margin-bottom: 1.5rem;">
                            <div>
                                <label class="form-label">Email Address *</label>
                                <input type="email" class="form-control" name="email" value="<?php echo htmlspecialchars($user_data['email'] ?? ''); ?>" required>
                            </div>
                            <div>
                                <label class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" name="phone" value="<?php echo htmlspecialchars($user_data['phone'] ?? ''); ?>">
                            </div>
                        </div>

                        <div style="margin-bottom: 1.5rem;">
                            <label class="form-label">Address</label>
                            <textarea class="form-control" name="address" rows="3"><?php echo htmlspecialchars($user_data['address'] ?? ''); ?></textarea>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1.5rem; margin-bottom: 1.5rem;">
                            <div>
                                <label class="form-label">Date of Birth</label>
                                <input type="date" class="form-control" name="date_of_birth" value="<?php echo $user_data['date_of_birth'] ?? ''; ?>">
                            </div>
                            <div>
                                <label class="form-label">Marital Status</label>
                                <select class="form-control" name="marital_status">
                                    <option value="">Select Status</option>
                                    <option value="single" <?php echo ($user_data['marital_status'] ?? '') === 'single' ? 'selected' : ''; ?>>Single</option>
                                    <option value="married" <?php echo ($user_data['marital_status'] ?? '') === 'married' ? 'selected' : ''; ?>>Married</option>
                                    <option value="divorced" <?php echo ($user_data['marital_status'] ?? '') === 'divorced' ? 'selected' : ''; ?>>Divorced</option>
                                    <option value="widowed" <?php echo ($user_data['marital_status'] ?? '') === 'widowed' ? 'selected' : ''; ?>>Widowed</option>
                                </select>
                            </div>
                            <div>
                                <label class="form-label">Gender</label>
                                <select class="form-control" name="gender">
                                    <option value="">Select Gender</option>
                                    <option value="male" <?php echo ($user_data['gender'] ?? '') === 'male' ? 'selected' : ''; ?>>Male</option>
                                    <option value="female" <?php echo ($user_data['gender'] ?? '') === 'female' ? 'selected' : ''; ?>>Female</option>
                                    <option value="other" <?php echo ($user_data['gender'] ?? '') === 'other' ? 'selected' : ''; ?>>Other</option>
                                </select>
                            </div>
                        </div>

                        <div style="margin-bottom: 1.5rem;">
                            <label class="form-label">Occupation</label>
                            <input type="text" class="form-control" name="occupation" value="<?php echo htmlspecialchars($user_data['occupation'] ?? ''); ?>">
                        </div>

                        <div style="text-align: right;">
                            <button type="submit" class="btn-primary">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                    <path d="M15.502 1.94a.5.5 0 010 .706L14.459 3.69l-2-2L13.502.646a.5.5 0 01.707 0l1.293 1.293zm-1.75 2.456l-2-2L4.939 9.21a.5.5 0 00-.121.196l-.805 2.414a.25.25 0 00.316.316l2.414-.805a.5.5 0 00.196-.12l6.813-6.814z"/>
                                    <path fill-rule="evenodd" d="M1 13.5A1.5 1.5 0 002.5 15h11a1.5 1.5 0 001.5-1.5v-6a.5.5 0 00-1 0v6a.5.5 0 01-.5.5h-11a.5.5 0 01-.5-.5v-11a.5.5 0 01.5-.5H9a.5.5 0 000-1H2.5A1.5 1.5 0 001 2.5v11z"/>
                                </svg>
                                Update Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Security Settings -->
            <div class="card" style="margin-top: 2rem;">
                <div class="card-header">
                    <h3 class="card-title">Security Settings</h3>
                </div>
                <div class="card-body" style="padding: 2rem;">
                    <form method="POST" id="passwordForm">
                        <input type="hidden" name="action" value="change_password">

                        <div style="margin-bottom: 1.5rem;">
                            <label class="form-label">Current Password *</label>
                            <input type="password" class="form-control" name="current_password" required>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem; margin-bottom: 1.5rem;">
                            <div>
                                <label class="form-label">New Password *</label>
                                <input type="password" class="form-control" name="new_password" minlength="8" required>
                                <small class="form-text text-muted">Minimum 8 characters</small>
                            </div>
                            <div>
                                <label class="form-label">Confirm New Password *</label>
                                <input type="password" class="form-control" name="confirm_password" minlength="8" required>
                            </div>
                        </div>

                        <div style="text-align: right;">
                            <button type="submit" class="btn-primary">
                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                    <path d="M8 1a2 2 0 012 2v4H6V3a2 2 0 012-2zM6 7v1.5a.5.5 0 001 0V7h2v1.5a.5.5 0 001 0V7h1a1 1 0 011 1v5a1 1 0 01-1 1H5a1 1 0 01-1-1V8a1 1 0 011-1h1z"/>
                                </svg>
                                Change Password
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar Section -->
        <div class="sidebar-section">
            <!-- Account Information -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Account Information</h3>
                </div>
                <div style="padding: 1.5rem;">
                    <div style="display: flex; flex-direction: column; gap: 1rem;">
                        <div>
                            <div style="font-size: 0.875rem; color: #6b7280; margin-bottom: 0.25rem;">Account Number</div>
                            <div style="font-weight: 600; font-family: monospace;"><?php echo htmlspecialchars($user_data['account_number'] ?? ''); ?></div>
                        </div>
                        <div>
                            <div style="font-size: 0.875rem; color: #6b7280; margin-bottom: 0.25rem;">Account Type</div>
                            <div style="font-weight: 600; text-transform: capitalize;"><?php echo htmlspecialchars($user_data['account_type'] ?? ''); ?></div>
                        </div>
                        <div>
                            <div style="font-size: 0.875rem; color: #6b7280; margin-bottom: 0.25rem;">Member Since</div>
                            <div style="font-weight: 600;"><?php echo date('F Y', strtotime($user_data['created_at'] ?? '')); ?></div>
                        </div>
                        <div>
                            <div style="font-size: 0.875rem; color: #6b7280; margin-bottom: 0.25rem;">KYC Status</div>
                            <span class="badge <?php echo ($user_data['kyc_status'] ?? '') === 'verified' ? 'status-completed' : 'status-pending'; ?>">
                                <?php echo ucfirst($user_data['kyc_status'] ?? 'pending'); ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Information -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Security Status</h3>
                </div>
                <div style="padding: 1.5rem;">
                    <div style="display: flex; flex-direction: column; gap: 1rem;">
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <div style="width: 8px; height: 8px; border-radius: 50%; background: #10b981;"></div>
                            <div>
                                <div style="font-weight: 500;">Password Protected</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">Last changed: Recently</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <div style="width: 8px; height: 8px; border-radius: 50%; background: #10b981;"></div>
                            <div>
                                <div style="font-weight: 500;">Account Verified</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">Email verified</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <div style="width: 8px; height: 8px; border-radius: 50%; background: <?php echo $recent_attempts > 0 ? '#f59e0b' : '#10b981'; ?>;"></div>
                            <div>
                                <div style="font-weight: 500;">Login Activity</div>
                                <div style="font-size: 0.875rem; color: #6b7280;"><?php echo $recent_attempts; ?> attempts (24h)</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Quick Actions</h3>
                </div>
                <div style="padding: 1.5rem;">
                    <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                        <button class="btn-outline" onclick="downloadAccountStatement()" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M.5 9.9a.5.5 0 01.5.5v2.5a1 1 0 001 1h12a1 1 0 001-1v-2.5a.5.5 0 011 0v2.5a2 2 0 01-2 2H2a2 2 0 01-2-2v-2.5a.5.5 0 01.5-.5z"/>
                                <path d="M7.646 11.854a.5.5 0 00.708 0l3-3a.5.5 0 00-.708-.708L8.5 10.293V1.5a.5.5 0 00-1 0v8.793L5.354 8.146a.5.5 0 10-.708.708l3 3z"/>
                            </svg>
                            Download Statement
                        </button>
                        <button class="btn-outline" onclick="exportAccountData()" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M4 0h5.293A1 1 0 0110 .293L13.707 4a1 1 0 01.293.707V14a2 2 0 01-2 2H4a2 2 0 01-2-2V2a2 2 0 012-2zm5.5 1.5v2a1 1 0 001 1h2l-3-3z"/>
                            </svg>
                            Export Data
                        </button>
                        <button class="btn-outline" onclick="contactSupport()" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z"/>
                            </svg>
                            Contact Support
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
document.getElementById('passwordForm').addEventListener('submit', function(e) {
    const newPassword = document.querySelector('input[name="new_password"]').value;
    const confirmPassword = document.querySelector('input[name="confirm_password"]').value;

    if (newPassword !== confirmPassword) {
        e.preventDefault();
        alert('New passwords do not match');
        return false;
    }

    if (newPassword.length < 8) {
        e.preventDefault();
        alert('Password must be at least 8 characters long');
        return false;
    }
});

function downloadAccountStatement() {
    alert('Account statement download functionality coming soon!');
}

function exportAccountData() {
    alert('Data export functionality coming soon!');
}

function contactSupport() {
    window.location.href = '../dashboard/help.php';
}
</script>

<?php
// Include footer
require_once '../includes/dashboard/footer.php';
?>
