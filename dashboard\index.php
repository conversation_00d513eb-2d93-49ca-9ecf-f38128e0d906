<?php
require_once '../config/config.php';
requireLogin();

$page_title = 'Dashboard';
$site_name = getBankName(); // Get bank name from settings

// Get user's recent transactions
try {
    $db = getDB();
    $sql = "SELECT t.*,
                   CASE
                       WHEN t.sender_id = ? THEN 'sent'
                       ELSE 'received'
                   END as direction,
                   CASE
                       WHEN t.sender_id = ? THEN t.recipient_name
                       ELSE (SELECT CONCAT(first_name, ' ', last_name) FROM accounts WHERE id = t.sender_id)
                   END as other_party
            FROM transfers t
            WHERE (t.sender_id = ? OR t.recipient_id = ?)
            AND t.status = 'completed'
            ORDER BY t.created_at DESC
            LIMIT 5";

    $user_id = $_SESSION['user_id'];
    $recent_transactions = $db->query($sql, [$user_id, $user_id, $user_id, $user_id]);

    // Get account balance (refresh from database)
    $balance_sql = "SELECT balance FROM accounts WHERE id = ?";
    $balance_result = $db->query($balance_sql, [$user_id]);
    $current_balance = $balance_result->fetch_assoc()['balance'];
    $_SESSION['balance'] = $current_balance; // Update session

    // Get monthly transaction summary
    $monthly_sql = "SELECT
                        COUNT(*) as total_transactions,
                        SUM(CASE WHEN sender_id = ? THEN amount ELSE 0 END) as total_sent,
                        SUM(CASE WHEN recipient_id = ? THEN amount ELSE 0 END) as total_received
                    FROM transfers
                    WHERE (sender_id = ? OR recipient_id = ?)
                    AND status = 'completed'
                    AND MONTH(created_at) = MONTH(CURRENT_DATE())
                    AND YEAR(created_at) = YEAR(CURRENT_DATE())";

    $monthly_result = $db->query($monthly_sql, [$user_id, $user_id, $user_id, $user_id]);
    $monthly_stats = $monthly_result->fetch_assoc();

} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $recent_transactions = null;
    $monthly_stats = ['total_transactions' => 0, 'total_sent' => 0, 'total_received' => 0];
}

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($page_title . ' - ' . $site_name); ?></title>
    
    <!-- Tabler CSS -->
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/css/tabler.min.css" rel="stylesheet"/>
    <link href="https://cdn.jsdelivr.net/npm/@tabler/icons@2.44.0/icons-sprite.svg" rel="stylesheet"/>
    
    <!-- Custom CSS -->
    <link href="../assets/css/dashboard.css" rel="stylesheet">
    
    <style>
        body {
            background: #f8f9fa;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .sidebar {
            background: #fff;
            border-right: 1px solid #e9ecef;
            width: 280px;
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
            padding: 1.5rem 0;
        }
        
        .sidebar-brand {
            padding: 0 1.5rem 2rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: #1a1a1a;
        }
        
        .sidebar-nav {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .sidebar-nav-item {
            margin: 0.25rem 1rem;
        }
        
        .sidebar-nav-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: #6c757d;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.2s;
        }
        
        .sidebar-nav-link:hover,
        .sidebar-nav-link.active {
            background: #f8f9fa;
            color: #495057;
        }
        
        .sidebar-nav-icon {
            width: 20px;
            height: 20px;
            margin-right: 0.75rem;
        }
        
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            padding: 2rem;
        }
        
        .top-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .top-bar h1 {
            font-size: 2rem;
            font-weight: 600;
            margin: 0;
            color: #1a1a1a;
        }
        
        .top-bar-actions {
            display: flex;
            gap: 1rem;
        }
        
        .btn-primary {
            background: #6366f1;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-outline {
            background: transparent;
            border: 1px solid #d1d5db;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            color: #374151;
            font-weight: 500;
            text-decoration: none;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 2rem;
        }
        
        .main-section {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }
        
        .sidebar-section {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }
        
        .balance-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            border: 1px solid #e5e7eb;
        }
        
        .balance-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }
        
        .balance-title {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0;
        }
        
        .balance-amount {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1a1a1a;
            margin: 0.5rem 0;
        }
        
        .balance-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .balance-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            font-size: 0.875rem;
            font-weight: 500;
            text-decoration: none;
            border: 1px solid #e5e7eb;
            background: white;
            color: #374151;
        }
        
        .quick-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .quick-action {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #6b7280;
            font-size: 0.875rem;
        }
        
        .quick-action-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: #f3f4f6;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0.5rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
        }
        
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
        }
        
        .stat-title {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0 0 0.5rem;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1a1a1a;
            margin: 0;
        }
        
        .stat-change {
            font-size: 0.75rem;
            color: #10b981;
            margin-top: 0.25rem;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }
        
        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1a1a1a;
            margin: 0;
        }
        
        .card-body {
            padding: 0;
        }
        .virtual-card {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            border-radius: 12px;
            padding: 1.5rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .virtual-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
        }

        .card-brand {
            text-align: right;
            font-weight: 700;
            font-size: 1.25rem;
            margin-bottom: 1rem;
        }

        .card-number {
            font-family: 'Courier New', monospace;
            font-size: 1.125rem;
            letter-spacing: 2px;
            margin-bottom: 1rem;
        }

        .card-details {
            display: flex;
            justify-content: space-between;
            font-size: 0.875rem;
        }

        .conversion-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
        }

        .conversion-title {
            font-size: 1rem;
            font-weight: 600;
            color: #1a1a1a;
            margin: 0 0 1rem;
        }

        .conversion-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .conversion-amount {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .conversion-currency {
            font-size: 0.875rem;
            color: #6b7280;
        }

        .conversion-btn {
            background: #6366f1;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            width: 100%;
            margin-top: 1rem;
        }

        .workflows-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid #e5e7eb;
        }

        .workflow-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 0.75rem;
            background: #f9fafb;
        }

        .workflow-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background: #1f2937;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .workflow-content {
            flex: 1;
        }

        .workflow-title {
            font-weight: 600;
            color: #1a1a1a;
            margin: 0 0 0.25rem;
        }

        .workflow-subtitle {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0;
        }

        .transaction-item {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f3f4f6;
        }

        .transaction-item:last-child {
            border-bottom: none;
        }

        .transaction-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            font-size: 0.875rem;
            font-weight: 600;
            color: white;
        }

        .transaction-content {
            flex: 1;
        }

        .transaction-title {
            font-weight: 500;
            color: #1a1a1a;
            margin: 0 0 0.25rem;
        }

        .transaction-subtitle {
            font-size: 0.875rem;
            color: #6b7280;
            margin: 0;
        }

        .transaction-amount {
            font-weight: 600;
            text-align: right;
        }

        .transaction-date {
            font-size: 0.75rem;
            color: #9ca3af;
            text-align: right;
            margin-top: 0.25rem;
        }

        .amount-positive {
            color: #10b981;
        }

        .amount-negative {
            color: #ef4444;
        }

        .status-completed {
            color: #10b981;
            background: #d1fae5;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-pending {
            color: #f59e0b;
            background: #fef3c7;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-brand">
            <?php echo htmlspecialchars($site_name); ?>
        </div>

        <ul class="sidebar-nav">
            <li class="sidebar-nav-item">
                <a href="#" class="sidebar-nav-link active">
                    <svg class="sidebar-nav-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                    </svg>
                    Dashboard
                </a>
            </li>
            <li class="sidebar-nav-item">
                <a href="../transfers/" class="sidebar-nav-link">
                    <svg class="sidebar-nav-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                    </svg>
                    Payments
                </a>
            </li>
            <li class="sidebar-nav-item">
                <a href="../cards/" class="sidebar-nav-link">
                    <svg class="sidebar-nav-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
                    </svg>
                    Cards
                </a>
            </li>
            <li class="sidebar-nav-item">
                <a href="../transfers/history.php" class="sidebar-nav-link">
                    <svg class="sidebar-nav-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clip-rule="evenodd"/>
                    </svg>
                    Invoices
                </a>
            </li>
            <li class="sidebar-nav-item">
                <a href="../analytics/" class="sidebar-nav-link">
                    <svg class="sidebar-nav-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
                    </svg>
                    Insights
                </a>
            </li>
            <li class="sidebar-nav-item">
                <a href="../rewards/" class="sidebar-nav-link">
                    <svg class="sidebar-nav-icon" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732L14.146 12.8l-1.179 4.456a1 1 0 01-1.934 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732L9.854 7.2l1.179-4.456A1 1 0 0112 2z" clip-rule="evenodd"/>
                    </svg>
                    Rewards
                </a>
            </li>
        </ul>

        <div style="margin-top: 2rem; padding: 0 1rem;">
            <div style="border-top: 1px solid #e5e7eb; padding-top: 1rem;">
                <ul class="sidebar-nav">
                    <li class="sidebar-nav-item">
                        <a href="../help/" class="sidebar-nav-link">
                            <svg class="sidebar-nav-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                            </svg>
                            Help
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="../feedback/" class="sidebar-nav-link">
                            <svg class="sidebar-nav-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 5v8a2 2 0 01-2 2h-5l-5 4v-4H4a2 2 0 01-2-2V5a2 2 0 012-2h12a2 2 0 012 2zM7 8H5v2h2V8zm2 0h2v2H9V8zm6 0h-2v2h2V8z" clip-rule="evenodd"/>
                            </svg>
                            Feedback
                        </a>
                    </li>
                </ul>
            </div>

            <div style="margin-top: 2rem; padding-top: 1rem; border-top: 1px solid #e5e7eb;">
                <ul class="sidebar-nav">
                    <li class="sidebar-nav-item">
                        <a href="../settings/" class="sidebar-nav-link">
                            <svg class="sidebar-nav-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"/>
                            </svg>
                            Settings
                        </a>
                    </li>
                    <li class="sidebar-nav-item">
                        <a href="../logout.php" class="sidebar-nav-link">
                            <svg class="sidebar-nav-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clip-rule="evenodd"/>
                            </svg>
                            Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <h1>Dashboard</h1>
            <div class="top-bar-actions">
                <a href="../transfers/" class="btn-outline">Create New</a>
                <a href="../transfers/" class="btn-outline">Add Funds</a>
                <a href="../transfers/" class="btn-primary">Move Money</a>
                <div style="width: 40px; height: 40px; border-radius: 50%; background: #6366f1; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                    <?php echo strtoupper(substr($_SESSION['first_name'] ?? 'U', 0, 1)); ?>
                </div>
            </div>
        </div>

        <!-- Dashboard Grid -->
        <div class="dashboard-grid">
            <!-- Main Section -->
            <div class="main-section">
                <!-- Balance Card -->
                <div class="balance-card">
                    <div class="balance-header">
                        <div>
                            <p class="balance-title">Total Balance In USD</p>
                            <h2 class="balance-amount">$<?php echo number_format($current_balance, 2); ?></h2>
                        </div>
                        <div style="width: 40px; height: 40px; border-radius: 50%; background: #6366f1; display: flex; align-items: center; justify-content: center;">
                            <svg width="20" height="20" fill="white" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                    </div>

                    <div class="balance-actions">
                        <a href="../transfers/" class="balance-btn">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                            </svg>
                            Send
                        </a>
                        <a href="../transfers/" class="balance-btn">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"/>
                            </svg>
                            Request
                        </a>
                        <a href="../transfers/" class="balance-btn">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8zM12 15a1 1 0 100-2H6.414l1.293-1.293a1 1 0 10-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L6.414 15H12z"/>
                            </svg>
                            Top-Up
                        </a>
                    </div>

                    <!-- Quick Actions -->
                    <div class="quick-actions">
                        <a href="../transfers/" class="quick-action">
                            <div class="quick-action-icon" style="background: #f59e0b;">
                                <svg width="20" height="20" fill="white" viewBox="0 0 20 20">
                                    <path d="M8 5a1 1 0 100 2h5.586l-1.293 1.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L13.586 5H8z"/>
                                </svg>
                            </div>
                            Transfer<br>$<?php echo number_format($monthly_stats['total_sent'] ?? 0, 0); ?>
                        </a>
                        <a href="../cards/" class="quick-action">
                            <div class="quick-action-icon" style="background: #8b5cf6;">
                                <svg width="20" height="20" fill="white" viewBox="0 0 20 20">
                                    <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"/>
                                    <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            Walton<br>$<?php echo number_format($monthly_stats['total_received'] ?? 0, 0); ?>
                        </a>
                        <a href="../analytics/" class="quick-action">
                            <div class="quick-action-icon" style="background: #10b981;">
                                <svg width="20" height="20" fill="white" viewBox="0 0 20 20">
                                    <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5z"/>
                                    <path d="M8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7z"/>
                                    <path d="M14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"/>
                                </svg>
                            </div>
                            Nathan<br>$<?php echo number_format(($monthly_stats['total_sent'] ?? 0) + ($monthly_stats['total_received'] ?? 0), 0); ?>
                        </a>
                        <a href="../rewards/" class="quick-action">
                            <div class="quick-action-icon" style="background: #ef4444;">
                                <svg width="20" height="20" fill="white" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            Angelina<br>$<?php echo number_format($monthly_stats['total_transactions'] ?? 0, 0); ?>
                        </a>
                        <a href="../help/" class="quick-action">
                            <div class="quick-action-icon" style="background: #6b7280;">
                                <svg width="20" height="20" fill="white" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            Thomas<br>$<?php echo number_format(rand(1000, 5000), 0); ?>
                        </a>
                        <a href="#" class="quick-action">
                            <div class="quick-action-icon" style="background: #d1d5db;">
                                <svg width="20" height="20" fill="white" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            Recent<br>$<?php echo number_format(rand(100, 999), 0); ?>
                        </a>
                    </div>
                </div>

                <!-- Stats Grid -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <p class="stat-title">Total Income</p>
                        <h3 class="stat-value">$<?php echo number_format($monthly_stats['total_received'] ?? 0, 0); ?></h3>
                        <div class="stat-change">+2.5% ↗</div>
                    </div>
                    <div class="stat-card">
                        <p class="stat-title">Total Spend</p>
                        <h3 class="stat-value">$<?php echo number_format($monthly_stats['total_sent'] ?? 0, 0); ?></h3>
                        <div class="stat-change">-2.5% ↘</div>
                    </div>
                </div>

                <!-- Transaction History -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Transaction history</h3>
                        <div style="display: flex; gap: 1rem; align-items: center;">
                            <span style="font-size: 0.875rem; color: #6b7280;">Last 30 days</span>
                            <button style="background: none; border: 1px solid #d1d5db; padding: 0.5rem 1rem; border-radius: 6px; font-size: 0.875rem;">Filter</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if ($recent_transactions && $recent_transactions->num_rows > 0): ?>
                            <?php while ($transaction = $recent_transactions->fetch_assoc()): ?>
                                <div class="transaction-item">
                                    <div class="transaction-icon" style="background: <?php echo $transaction['direction'] === 'sent' ? '#ef4444' : '#10b981'; ?>;">
                                        <?php echo strtoupper(substr($transaction['other_party'] ?? 'U', 0, 2)); ?>
                                    </div>
                                    <div class="transaction-content">
                                        <div class="transaction-title"><?php echo htmlspecialchars($transaction['other_party'] ?? 'Unknown'); ?></div>
                                        <div class="transaction-subtitle"><?php echo ucfirst($transaction['direction']); ?> • <?php echo htmlspecialchars($transaction['description'] ?? ''); ?></div>
                                    </div>
                                    <div>
                                        <div class="transaction-amount <?php echo $transaction['direction'] === 'sent' ? 'amount-negative' : 'amount-positive'; ?>">
                                            <?php echo $transaction['direction'] === 'sent' ? '-' : '+'; ?>$<?php echo number_format($transaction['amount'], 2); ?>
                                        </div>
                                        <div class="transaction-date"><?php echo date('M d, Y', strtotime($transaction['created_at'])); ?></div>
                                        <div class="status-completed">Completed</div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <div style="padding: 2rem; text-align: center; color: #6b7280;">
                                No transactions found
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Sidebar Section -->
            <div class="sidebar-section">
                <!-- My Cards -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">My Cards</h3>
                        <a href="../cards/" style="color: #6366f1; font-size: 0.875rem; text-decoration: none;">Add New +</a>
                    </div>
                    <div style="padding: 1.5rem;">
                        <div class="virtual-card">
                            <div class="card-brand">VISA</div>
                            <div style="margin-bottom: 1rem;">
                                <div style="font-size: 0.875rem; opacity: 0.8;">Thomas Fletcher</div>
                            </div>
                            <div class="card-number">1253 5432 3521 3090</div>
                            <div class="card-details">
                                <div>
                                    <div style="font-size: 0.75rem; opacity: 0.8;">Exp</div>
                                    <div>05/24</div>
                                </div>
                                <div>
                                    <div style="font-size: 0.75rem; opacity: 0.8;">CVV</div>
                                    <div>***</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Conversion Rate -->
                <div class="conversion-card">
                    <h3 class="conversion-title">Conversion Rate</h3>
                    <div class="conversion-row">
                        <div>
                            <div class="conversion-currency">Amount</div>
                        </div>
                        <div style="text-align: right;">
                            <div class="conversion-currency">USD</div>
                        </div>
                    </div>
                    <div class="conversion-row">
                        <div class="conversion-amount">238</div>
                        <div class="conversion-amount" style="text-align: right;">USD</div>
                    </div>
                    <div style="margin: 1rem 0; height: 1px; background: #e5e7eb;"></div>
                    <div class="conversion-row">
                        <div class="conversion-amount">222.13</div>
                        <div class="conversion-amount" style="text-align: right;">EUR</div>
                    </div>
                    <button class="conversion-btn">Convert → 238 USD = 222.13 EUR</button>
                </div>

                <!-- Workflows -->
                <div class="workflows-card">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1.5rem;">
                        <h3 style="margin: 0; font-size: 1.125rem; font-weight: 600;">Workflows</h3>
                        <a href="#" style="color: #6366f1; font-size: 0.875rem; text-decoration: none;">+</a>
                    </div>

                    <div class="workflow-item">
                        <div class="workflow-icon">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4z"/>
                                <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="workflow-content">
                            <div class="workflow-title">Transactions</div>
                            <div class="workflow-subtitle">Auto Block</div>
                        </div>
                        <svg width="16" height="16" fill="#6b7280" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                        </svg>
                    </div>

                    <div class="workflow-item">
                        <div class="workflow-icon">
                            <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                        <div class="workflow-content">
                            <div class="workflow-title">Create order</div>
                            <div class="workflow-subtitle">Looks OK</div>
                        </div>
                        <svg width="16" height="16" fill="#6b7280" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                        </svg>
                    </div>

                    <div style="text-align: center; margin-top: 1rem;">
                        <a href="#" style="color: #6b7280; font-size: 0.875rem; text-decoration: none;">Upcoming →</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabler JS -->
    <script src="https://cdn.jsdelivr.net/npm/@tabler/core@1.0.0-beta17/dist/js/tabler.min.js"></script>
</body>
</html>
