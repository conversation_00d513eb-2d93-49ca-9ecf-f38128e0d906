<?php
require_once '../config/config.php';
requireLogin();

$page_title = 'Feedback & Suggestions';
$site_name = getBankName();

// Handle feedback submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'submit_feedback') {
    try {
        $db = getDB();
        $user_id = $_SESSION['user_id'];

        $type = $_POST['type'] ?? 'general';
        $rating = intval($_POST['rating'] ?? 0);
        $subject = trim($_POST['subject'] ?? '');
        $message = trim($_POST['message'] ?? '');
        $anonymous = isset($_POST['anonymous']) ? 1 : 0;

        // Validate inputs
        if (empty($subject) || empty($message)) {
            throw new Exception('Subject and message are required');
        }

        if ($rating < 1 || $rating > 5) {
            throw new Exception('Please provide a valid rating (1-5 stars)');
        }

        if (!in_array($type, ['general', 'feature_request', 'bug_report', 'compliment', 'complaint'])) {
            $type = 'general';
        }

        // Generate feedback number
        $feedback_number = 'FB-' . date('Ymd') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);

        // Insert feedback as a ticket with feedback type
        $insert_sql = "INSERT INTO tickets (ticket_number, user_id, subject, message, status, priority, created_at)
                       VALUES (?, ?, ?, ?, 'open', 'low', NOW())";

        $feedback_subject = "[$type] $subject";
        $feedback_message = "Rating: $rating/5 stars\nType: $type\nAnonymous: " . ($anonymous ? 'Yes' : 'No') . "\n\n$message";

        $result = $db->query($insert_sql, [$feedback_number, $user_id, $feedback_subject, $feedback_message]);

        if ($result) {
            $success_message = "Thank you for your feedback! Reference number: $feedback_number";
        } else {
            throw new Exception('Failed to submit feedback');
        }

    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}

// Get user's feedback history
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    // Get user's feedback (tickets that start with feedback types)
    $feedback_sql = "SELECT * FROM tickets
                     WHERE user_id = ?
                     AND (subject LIKE '[general]%' OR subject LIKE '[feature_request]%' OR
                          subject LIKE '[bug_report]%' OR subject LIKE '[compliment]%' OR
                          subject LIKE '[complaint]%')
                     ORDER BY created_at DESC LIMIT 10";

    $feedback_result = $db->query($feedback_sql, [$user_id]);
    $user_feedback = [];
    while ($feedback = $feedback_result->fetch_assoc()) {
        $user_feedback[] = $feedback;
    }

} catch (Exception $e) {
    error_log("Feedback page error: " . $e->getMessage());
    $user_feedback = [];
}

// Include header
require_once '../includes/dashboard/header.php';

// Include sidebar
require_once '../includes/dashboard/sidebar.php';
?>

<!-- Main Content -->
<div class="main-content">
    <!-- Top Bar -->
    <div class="top-bar">
        <h1>Feedback & Suggestions</h1>
        <div class="top-bar-actions">
            <button class="btn-outline" onclick="showFeedbackModal()">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M8 4a.5.5 0 01.5.5v3h3a.5.5 0 010 1h-3v3a.5.5 0 01-1 0v-3h-3a.5.5 0 010-1h3v-3A.5.5 0 018 4z"/>
                </svg>
                Give Feedback
            </button>
            <button class="btn-primary" onclick="window.location.reload()">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path fill-rule="evenodd" d="M8 3a5 5 0 104.546 2.914.5.5 0 00-.908-.417A4 4 0 118 4v1z"/>
                    <path d="M8 4.466V.534a.25.25 0 01.41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 018 4.466z"/>
                </svg>
                Refresh
            </button>
            <div style="width: 40px; height: 40px; border-radius: 50%; background: #6366f1; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                <?php echo strtoupper(substr($_SESSION['first_name'] ?? 'U', 0, 1)); ?>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (isset($success_message)): ?>
        <div class="alert alert-success" style="margin-bottom: 2rem; padding: 1rem; background: #d1fae5; border: 1px solid #10b981; border-radius: 8px; color: #065f46;">
            <strong>Thank you!</strong> <?php echo htmlspecialchars($success_message); ?>
        </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger" style="margin-bottom: 2rem; padding: 1rem; background: #fee2e2; border: 1px solid #ef4444; border-radius: 8px; color: #991b1b;">
            <strong>Error!</strong> <?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif; ?>

    <!-- Feedback Categories -->
    <div class="stats-grid" style="margin-bottom: 2rem;">
        <div class="stat-card" style="cursor: pointer;" onclick="showFeedbackModal('feature_request')">
            <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 1rem;">
                <div style="width: 40px; height: 40px; border-radius: 50%; background: #6366f1; display: flex; align-items: center; justify-content: center; color: white;">
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M8 4a.5.5 0 01.5.5v3h3a.5.5 0 010 1h-3v3a.5.5 0 01-1 0v-3h-3a.5.5 0 010-1h3v-3A.5.5 0 018 4z"/>
                    </svg>
                </div>
                <h3 class="stat-title" style="margin: 0;">Feature Request</h3>
            </div>
            <p style="color: #6b7280; font-size: 0.875rem;">Suggest new features or improvements</p>
        </div>
        <div class="stat-card" style="cursor: pointer;" onclick="showFeedbackModal('bug_report')">
            <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 1rem;">
                <div style="width: 40px; height: 40px; border-radius: 50%; background: #ef4444; display: flex; align-items: center; justify-content: center; color: white;">
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M4.355.522a.5.5 0 01.623.333l.291.956A4.979 4.979 0 018 1c1.007 0 1.946.298 2.731.811l.291-.956a.5.5 0 11.956.29l-.41 1.352A4.985 4.985 0 0113 6h.5a.5.5 0 010 1H13v1h1.5a.5.5 0 010 1H13v1h.5a.5.5 0 010 1H13a5 5 0 01-10 0h-.5a.5.5 0 010-1H3V9H1.5a.5.5 0 010-1H3V7h-.5a.5.5 0 010-1H3c0-1.364.547-2.601 1.432-3.503l-.41-1.352a.5.5 0 01.333-.623zM4 7v4a4 4 0 008 0V7a4 4 0 00-8 0z"/>
                    </svg>
                </div>
                <h3 class="stat-title" style="margin: 0;">Bug Report</h3>
            </div>
            <p style="color: #6b7280; font-size: 0.875rem;">Report issues or problems</p>
        </div>
        <div class="stat-card" style="cursor: pointer;" onclick="showFeedbackModal('compliment')">
            <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 1rem;">
                <div style="width: 40px; height: 40px; border-radius: 50%; background: #10b981; display: flex; align-items: center; justify-content: center; color: white;">
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M8 15A7 7 0 118 1a7 7 0 010 14zm0 1A8 8 0 108 0a8 8 0 000 16z"/>
                        <path d="M10.97 4.97a.235.235 0 00-.02-.022L7.477 9.417 5.384 7.323a.75.75 0 00-1.06 1.06L6.97 11.03a.75.75 0 001.079-.02l3.992-4.99a.75.75 0 00-1.071-1.05z"/>
                    </svg>
                </div>
                <h3 class="stat-title" style="margin: 0;">Compliment</h3>
            </div>
            <p style="color: #6b7280; font-size: 0.875rem;">Share positive feedback</p>
        </div>
        <div class="stat-card" style="cursor: pointer;" onclick="showFeedbackModal('general')">
            <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 1rem;">
                <div style="width: 40px; height: 40px; border-radius: 50%; background: #f59e0b; display: flex; align-items: center; justify-content: center; color: white;">
                    <svg width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                        <path fill-rule="evenodd" d="M18 5v8a2 2 0 01-2 2h-5l-5 4v-4H4a2 2 0 01-2-2V5a2 2 0 012-2h12a2 2 0 012 2zM7 8H5v2h2V8zm2 0h2v2H9V8zm6 0h-2v2h2V8z"/>
                    </svg>
                </div>
                <h3 class="stat-title" style="margin: 0;">General Feedback</h3>
            </div>
            <p style="color: #6b7280; font-size: 0.875rem;">General comments or suggestions</p>
        </div>
    </div>

    <!-- Main Dashboard Grid -->
    <div class="dashboard-grid">
        <div class="main-section">
            <!-- Your Feedback History -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Your Feedback History</h3>
                    <button class="btn-outline" onclick="showFeedbackModal()">Give New Feedback</button>
                </div>
                <div class="card-body">
                    <?php if (!empty($user_feedback)): ?>
                        <?php foreach ($user_feedback as $feedback):
                            // Extract feedback type from subject
                            preg_match('/\[(.*?)\]/', $feedback['subject'], $matches);
                            $feedback_type = $matches[1] ?? 'general';
                            $clean_subject = preg_replace('/\[.*?\]\s*/', '', $feedback['subject']);
                        ?>
                            <div class="transaction-item">
                                <div class="transaction-icon" style="background: <?php
                                    echo $feedback_type === 'compliment' ? '#10b981' :
                                        ($feedback_type === 'bug_report' ? '#ef4444' :
                                        ($feedback_type === 'feature_request' ? '#6366f1' : '#f59e0b'));
                                ?>;">
                                    <?php if ($feedback_type === 'compliment'): ?>
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path d="M10.97 4.97a.235.235 0 00-.02-.022L7.477 9.417 5.384 7.323a.75.75 0 00-1.06 1.06L6.97 11.03a.75.75 0 001.079-.02l3.992-4.99a.75.75 0 00-1.071-1.05z"/>
                                        </svg>
                                    <?php elseif ($feedback_type === 'bug_report'): ?>
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path d="M4.355.522a.5.5 0 01.623.333l.291.956A4.979 4.979 0 018 1c1.007 0 1.946.298 2.731.811l.291-.956a.5.5 0 11.956.29l-.41 1.352A4.985 4.985 0 0113 6h.5a.5.5 0 010 1H13v1h1.5a.5.5 0 010 1H13v1h.5a.5.5 0 010 1H13a5 5 0 01-10 0h-.5a.5.5 0 010-1H3V9H1.5a.5.5 0 010-1H3V7h-.5a.5.5 0 010-1H3c0-1.364.547-2.601 1.432-3.503l-.41-1.352a.5.5 0 01.333-.623zM4 7v4a4 4 0 008 0V7a4 4 0 00-8 0z"/>
                                        </svg>
                                    <?php elseif ($feedback_type === 'feature_request'): ?>
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path d="M8 4a.5.5 0 01.5.5v3h3a.5.5 0 010 1h-3v3a.5.5 0 01-1 0v-3h-3a.5.5 0 010-1h3v-3A.5.5 0 018 4z"/>
                                        </svg>
                                    <?php else: ?>
                                        <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                            <path fill-rule="evenodd" d="M18 5v8a2 2 0 01-2 2h-5l-5 4v-4H4a2 2 0 01-2-2V5a2 2 0 012-2h12a2 2 0 012 2zM7 8H5v2h2V8zm2 0h2v2H9V8zm6 0h-2v2h2V8z"/>
                                        </svg>
                                    <?php endif; ?>
                                </div>
                                <div class="transaction-content">
                                    <div class="transaction-title">
                                        <?php echo htmlspecialchars($clean_subject); ?>
                                    </div>
                                    <div class="transaction-subtitle">
                                        <?php echo ucfirst(str_replace('_', ' ', $feedback_type)); ?> •
                                        Ref: <?php echo htmlspecialchars($feedback['ticket_number']); ?>
                                    </div>
                                </div>
                                <div style="text-align: right;">
                                    <span class="<?php echo $feedback['status'] === 'resolved' ? 'status-completed' : 'status-pending'; ?>">
                                        <?php echo ucfirst(str_replace('_', ' ', $feedback['status'])); ?>
                                    </span>
                                    <div class="transaction-date">
                                        <?php echo date('M j, Y', strtotime($feedback['created_at'])); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div style="text-align: center; padding: 3rem 1rem;">
                            <div style="font-size: 3rem; color: #e5e7eb; margin-bottom: 1rem;">💬</div>
                            <h3 style="color: #6b7280; margin-bottom: 0.5rem;">No Feedback Yet</h3>
                            <p style="color: #9ca3af; margin-bottom: 2rem;">We'd love to hear from you! Share your thoughts and help us improve.</p>
                            <button class="btn-primary" onclick="showFeedbackModal()">Give Your First Feedback</button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar Section -->
        <div class="sidebar-section">
            <!-- Quick Feedback -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Quick Feedback</h3>
                </div>
                <div style="padding: 1.5rem;">
                    <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                        <button class="btn-primary" onclick="showFeedbackModal('compliment')" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M10.97 4.97a.235.235 0 00-.02-.022L7.477 9.417 5.384 7.323a.75.75 0 00-1.06 1.06L6.97 11.03a.75.75 0 001.079-.02l3.992-4.99a.75.75 0 00-1.071-1.05z"/>
                            </svg>
                            Give Compliment
                        </button>
                        <button class="btn-outline" onclick="showFeedbackModal('feature_request')" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M8 4a.5.5 0 01.5.5v3h3a.5.5 0 010 1h-3v3a.5.5 0 01-1 0v-3h-3a.5.5 0 010-1h3v-3A.5.5 0 018 4z"/>
                            </svg>
                            Request Feature
                        </button>
                        <button class="btn-outline" onclick="showFeedbackModal('bug_report')" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M4.355.522a.5.5 0 01.623.333l.291.956A4.979 4.979 0 018 1c1.007 0 1.946.298 2.731.811l.291-.956a.5.5 0 11.956.29l-.41 1.352A4.985 4.985 0 0113 6h.5a.5.5 0 010 1H13v1h1.5a.5.5 0 010 1H13v1h.5a.5.5 0 010 1H13a5 5 0 01-10 0h-.5a.5.5 0 010-1H3V9H1.5a.5.5 0 010-1H3V7h-.5a.5.5 0 010-1H3c0-1.364.547-2.601 1.432-3.503l-.41-1.352a.5.5 0 01.333-.623zM4 7v4a4 4 0 008 0V7a4 4 0 00-8 0z"/>
                            </svg>
                            Report Bug
                        </button>
                    </div>
                </div>
            </div>

            <!-- Feedback Tips -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Feedback Tips</h3>
                </div>
                <div style="padding: 1.5rem;">
                    <div style="display: flex; flex-direction: column; gap: 1rem;">
                        <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                            <div style="width: 8px; height: 8px; border-radius: 50%; background: #6366f1; margin-top: 0.5rem; flex-shrink: 0;"></div>
                            <div>
                                <div style="font-weight: 500; margin-bottom: 0.25rem;">Be Specific</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">Provide detailed information about your experience</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                            <div style="width: 8px; height: 8px; border-radius: 50%; background: #6366f1; margin-top: 0.5rem; flex-shrink: 0;"></div>
                            <div>
                                <div style="font-weight: 500; margin-bottom: 0.25rem;">Include Steps</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">For bugs, include steps to reproduce the issue</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                            <div style="width: 8px; height: 8px; border-radius: 50%; background: #6366f1; margin-top: 0.5rem; flex-shrink: 0;"></div>
                            <div>
                                <div style="font-weight: 500; margin-bottom: 0.25rem;">Be Constructive</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">Focus on how we can improve your experience</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Updates -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Recent Updates</h3>
                </div>
                <div style="padding: 1rem;">
                    <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                        <div style="padding: 0.75rem; border: 1px solid #e5e7eb; border-radius: 8px;">
                            <div style="font-weight: 500; margin-bottom: 0.25rem; color: #10b981;">✓ New Feature</div>
                            <div style="font-size: 0.875rem; color: #6b7280; margin-bottom: 0.25rem;">Virtual card management</div>
                            <div style="font-size: 0.75rem; color: #9ca3af;">Based on your feedback</div>
                        </div>
                        <div style="padding: 0.75rem; border: 1px solid #e5e7eb; border-radius: 8px;">
                            <div style="font-weight: 500; margin-bottom: 0.25rem; color: #6366f1;">🔧 Improvement</div>
                            <div style="font-size: 0.875rem; color: #6b7280; margin-bottom: 0.25rem;">Faster transfer processing</div>
                            <div style="font-size: 0.75rem; color: #9ca3af;">Performance enhancement</div>
                        </div>
                        <div style="padding: 0.75rem; border: 1px solid #e5e7eb; border-radius: 8px;">
                            <div style="font-weight: 500; margin-bottom: 0.25rem; color: #ef4444;">🐛 Bug Fix</div>
                            <div style="font-size: 0.875rem; color: #6b7280; margin-bottom: 0.25rem;">Login session timeout</div>
                            <div style="font-size: 0.75rem; color: #9ca3af;">Resolved security issue</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Feedback Modal -->
<div class="modal fade" id="feedbackModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Share Your Feedback</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <input type="hidden" name="action" value="submit_feedback">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Feedback Type *</label>
                                <select class="form-control" name="type" id="feedbackType">
                                    <option value="general">General Feedback</option>
                                    <option value="feature_request">Feature Request</option>
                                    <option value="bug_report">Bug Report</option>
                                    <option value="compliment">Compliment</option>
                                    <option value="complaint">Complaint</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Rating *</label>
                                <div style="display: flex; gap: 0.25rem; align-items: center;">
                                    <div class="star-rating">
                                        <input type="radio" name="rating" value="1" id="star1">
                                        <label for="star1">⭐</label>
                                        <input type="radio" name="rating" value="2" id="star2">
                                        <label for="star2">⭐</label>
                                        <input type="radio" name="rating" value="3" id="star3">
                                        <label for="star3">⭐</label>
                                        <input type="radio" name="rating" value="4" id="star4">
                                        <label for="star4">⭐</label>
                                        <input type="radio" name="rating" value="5" id="star5">
                                        <label for="star5">⭐</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Subject *</label>
                        <input type="text" class="form-control" name="subject" required placeholder="Brief summary of your feedback">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Message *</label>
                        <textarea class="form-control" name="message" rows="6" required placeholder="Please share your detailed feedback..."></textarea>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="anonymous" id="anonymous">
                            <label class="form-check-label" for="anonymous">
                                Submit anonymously (your name won't be shared with our team)
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Submit Feedback</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.star-rating {
    display: flex;
    flex-direction: row-reverse;
    gap: 0.25rem;
}

.star-rating input {
    display: none;
}

.star-rating label {
    cursor: pointer;
    font-size: 1.5rem;
    color: #d1d5db;
    transition: color 0.2s;
}

.star-rating input:checked ~ label,
.star-rating label:hover,
.star-rating label:hover ~ label {
    color: #f59e0b;
}
</style>

<script>
function showFeedbackModal(type = 'general') {
    const modal = new bootstrap.Modal(document.getElementById('feedbackModal'));

    // Set the feedback type if provided
    if (type) {
        document.getElementById('feedbackType').value = type;
    }

    modal.show();
}
</script>

<?php
// Include footer
require_once '../includes/dashboard/footer.php';
?>
