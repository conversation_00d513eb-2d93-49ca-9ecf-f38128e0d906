<?php
require_once '../../config/config.php';
requireLogin();

header('Content-Type: application/json');

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }
    
    $user_id = $_SESSION['user_id'];
    $card_holder_name = trim($_POST['card_holder_name'] ?? '');
    $card_type = trim($_POST['card_type'] ?? '');
    $spending_limit = floatval($_POST['spending_limit'] ?? 0);
    
    // Validate inputs
    if (empty($card_holder_name)) {
        throw new Exception('Card holder name is required');
    }
    
    if (!in_array($card_type, ['visa', 'mastercard', 'amex'])) {
        throw new Exception('Invalid card type');
    }
    
    if ($spending_limit < 100 || $spending_limit > 10000) {
        throw new Exception('Spending limit must be between $100 and $10,000');
    }
    
    $db = getDB();
    
    // Check if user already has maximum cards (e.g., 5 cards)
    $count_result = $db->query("SELECT COUNT(*) as count FROM virtual_cards WHERE user_id = ?", [$user_id]);
    $card_count = $count_result->fetch_assoc()['count'];
    
    if ($card_count >= 5) {
        throw new Exception('Maximum number of virtual cards reached (5 cards)');
    }
    
    // Generate card number (16 digits)
    $card_number = generateCardNumber($card_type);
    
    // Generate expiry date (3 years from now)
    $expiry_month = date('n');
    $expiry_year = date('Y') + 3;
    
    // Generate CVV (3 or 4 digits depending on card type)
    $cvv_length = ($card_type === 'amex') ? 4 : 3;
    $cvv = str_pad(rand(0, pow(10, $cvv_length) - 1), $cvv_length, '0', STR_PAD_LEFT);
    
    // Insert new virtual card
    $insert_sql = "INSERT INTO virtual_cards (
        user_id, card_number, card_holder_name, expiry_month, expiry_year, 
        cvv, card_type, status, spending_limit, current_balance, created_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, 'active', ?, 0.00, NOW())";
    
    $result = $db->query($insert_sql, [
        $user_id, $card_number, $card_holder_name, $expiry_month, $expiry_year,
        $cvv, $card_type, $spending_limit
    ]);
    
    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Virtual card created successfully',
            'card_id' => $db->lastInsertId()
        ]);
    } else {
        throw new Exception('Failed to create virtual card');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function generateCardNumber($card_type) {
    // Generate card number based on card type
    switch ($card_type) {
        case 'visa':
            // Visa cards start with 4
            $prefix = '4';
            $length = 16;
            break;
        case 'mastercard':
            // Mastercard starts with 5
            $prefix = '5';
            $length = 16;
            break;
        case 'amex':
            // American Express starts with 3
            $prefix = '3';
            $length = 15;
            break;
        default:
            $prefix = '4';
            $length = 16;
    }
    
    // Generate remaining digits
    $remaining_length = $length - strlen($prefix);
    $remaining_digits = '';
    
    for ($i = 0; $i < $remaining_length; $i++) {
        $remaining_digits .= rand(0, 9);
    }
    
    return $prefix . $remaining_digits;
}
?>
