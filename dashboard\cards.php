<?php
require_once '../config/config.php';
requireLogin();

$page_title = 'Virtual Cards';
$site_name = getBankName();

// Get user's virtual cards
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    // Get user's virtual cards
    $cards_sql = "SELECT * FROM virtual_cards WHERE user_id = ? ORDER BY created_at DESC";
    $cards_result = $db->query($cards_sql, [$user_id]);
    $virtual_cards = [];
    while ($card = $cards_result->fetch_assoc()) {
        $virtual_cards[] = $card;
    }

    // Get card statistics
    $stats_sql = "SELECT
                    COUNT(*) as total_cards,
                    SUM(current_balance) as total_balance,
                    SUM(spending_limit) as total_limit,
                    COUNT(CASE WHEN status = 'active' THEN 1 END) as active_cards
                  FROM virtual_cards WHERE user_id = ?";
    $stats_result = $db->query($stats_sql, [$user_id]);
    $card_stats = $stats_result->fetch_assoc();

} catch (Exception $e) {
    error_log("Cards page error: " . $e->getMessage());
    $virtual_cards = [];
    $card_stats = ['total_cards' => 0, 'total_balance' => 0, 'total_limit' => 0, 'active_cards' => 0];
}

// Include header
require_once '../includes/dashboard/header.php';

// Include sidebar
require_once '../includes/dashboard/sidebar.php';
?>

<!-- Main Content -->
<div class="main-content">
    <!-- Top Bar -->
    <div class="top-bar">
        <h1>Virtual Cards</h1>
        <div class="top-bar-actions">
            <button class="btn-outline" onclick="showCreateCardModal()">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M8 4a.5.5 0 01.5.5v3h3a.5.5 0 010 1h-3v3a.5.5 0 01-1 0v-3h-3a.5.5 0 010-1h3v-3A.5.5 0 018 4z"/>
                </svg>
                Create Card
            </button>
            <button class="btn-primary" onclick="window.location.reload()">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path fill-rule="evenodd" d="M8 3a5 5 0 104.546 2.914.5.5 0 00-.908-.417A4 4 0 118 4v1z"/>
                    <path d="M8 4.466V.534a.25.25 0 01.41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 018 4.466z"/>
                </svg>
                Refresh
            </button>
            <div style="width: 40px; height: 40px; border-radius: 50%; background: #6366f1; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                <?php echo strtoupper(substr($_SESSION['first_name'] ?? 'U', 0, 1)); ?>
            </div>
        </div>
    </div>

    <!-- Cards Statistics -->
    <div class="stats-grid" style="margin-bottom: 2rem;">
        <div class="stat-card">
            <h3 class="stat-title">Total Cards</h3>
            <p class="stat-value"><?php echo number_format($card_stats['total_cards']); ?></p>
            <p class="stat-change">
                <?php echo number_format($card_stats['active_cards']); ?> active
            </p>
        </div>
        <div class="stat-card">
            <h3 class="stat-title">Total Balance</h3>
            <p class="stat-value">$<?php echo number_format($card_stats['total_balance'], 2); ?></p>
            <p class="stat-change">Across all cards</p>
        </div>
        <div class="stat-card">
            <h3 class="stat-title">Total Limit</h3>
            <p class="stat-value">$<?php echo number_format($card_stats['total_limit'], 2); ?></p>
            <p class="stat-change">Combined spending limit</p>
        </div>
        <div class="stat-card">
            <h3 class="stat-title">Available Credit</h3>
            <p class="stat-value">$<?php echo number_format($card_stats['total_limit'] - $card_stats['total_balance'], 2); ?></p>
            <p class="stat-change">Ready to spend</p>
        </div>
    </div>

    <!-- Cards Grid -->
    <div class="dashboard-grid">
        <div class="main-section">
            <!-- Virtual Cards List -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Your Virtual Cards</h3>
                    <button class="btn-outline" onclick="showCreateCardModal()">Add New Card</button>
                </div>
                <div class="card-body">
                    <?php if (!empty($virtual_cards)): ?>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 1.5rem;">
                            <?php foreach ($virtual_cards as $card): ?>
                                <div class="virtual-card" style="position: relative;">
                                    <div class="card-brand" style="display: flex; justify-content: space-between; align-items: center;">
                                        <span><?php echo strtoupper($card['card_type']); ?></span>
                                        <span class="badge <?php echo $card['status'] === 'active' ? 'status-completed' : 'status-pending'; ?>">
                                            <?php echo ucfirst($card['status']); ?>
                                        </span>
                                    </div>
                                    <div style="margin-bottom: 1rem;">
                                        <div style="font-size: 0.875rem; opacity: 0.8;">
                                            <?php echo htmlspecialchars($card['card_holder_name']); ?>
                                        </div>
                                    </div>
                                    <div class="card-number" style="margin-bottom: 1rem;">
                                        <?php
                                        // Mask card number for security
                                        $masked_number = substr($card['card_number'], 0, 4) . ' **** **** ' . substr($card['card_number'], -4);
                                        echo $masked_number;
                                        ?>
                                    </div>
                                    <div class="card-details">
                                        <div>
                                            <div style="font-size: 0.75rem; opacity: 0.8;">Exp</div>
                                            <div><?php echo sprintf('%02d/%02d', $card['expiry_month'], $card['expiry_year']); ?></div>
                                        </div>
                                        <div>
                                            <div style="font-size: 0.75rem; opacity: 0.8;">CVV</div>
                                            <div>***</div>
                                        </div>
                                        <div>
                                            <div style="font-size: 0.75rem; opacity: 0.8;">Balance</div>
                                            <div>$<?php echo number_format($card['current_balance'], 2); ?></div>
                                        </div>
                                    </div>

                                    <!-- Card Actions -->
                                    <div style="position: absolute; top: 10px; right: 10px;">
                                        <div class="dropdown">
                                            <button class="btn btn-sm" style="background: rgba(255,255,255,0.2); border: none; color: white;" data-bs-toggle="dropdown">
                                                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                                    <path d="M3 9.5a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3zm5 0a1.5 1.5 0 1 1 0-3 1.5 1.5 0 0 1 0 3z"/>
                                                </svg>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" onclick="viewCardDetails(<?php echo $card['id']; ?>)">View Details</a></li>
                                                <li><a class="dropdown-item" href="#" onclick="topUpCard(<?php echo $card['id']; ?>)">Top Up</a></li>
                                                <li><a class="dropdown-item" href="#" onclick="freezeCard(<?php echo $card['id']; ?>)">
                                                    <?php echo $card['status'] === 'active' ? 'Freeze Card' : 'Unfreeze Card'; ?>
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteCard(<?php echo $card['id']; ?>)">Delete Card</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div style="text-align: center; padding: 3rem 1rem;">
                            <div style="font-size: 3rem; color: #e5e7eb; margin-bottom: 1rem;">💳</div>
                            <h3 style="color: #6b7280; margin-bottom: 0.5rem;">No Virtual Cards</h3>
                            <p style="color: #9ca3af; margin-bottom: 2rem;">Create your first virtual card to start making secure online payments.</p>
                            <button class="btn-primary" onclick="showCreateCardModal()">Create Your First Card</button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar Section -->
        <div class="sidebar-section">
            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Quick Actions</h3>
                </div>
                <div style="padding: 1.5rem;">
                    <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                        <button class="btn-primary" onclick="showCreateCardModal()" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M8 4a.5.5 0 01.5.5v3h3a.5.5 0 010 1h-3v3a.5.5 0 01-1 0v-3h-3a.5.5 0 010-1h3v-3A.5.5 0 018 4z"/>
                            </svg>
                            Create New Card
                        </button>
                        <button class="btn-outline" onclick="showTopUpModal()" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M1 3a1 1 0 011-1h12a1 1 0 011 1H1zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zM9 8a1 1 0 012 0v6a1 1 0 11-2 0V8z"/>
                                <path fill-rule="evenodd" d="M14.5 3a1 1 0 01-1 1H13v9a2 2 0 01-2 2H5a2 2 0 01-2-2V4h-.5a1 1 0 01-1-1V2a1 1 0 011-1H6a1 1 0 011-1h2a1 1 0 011 1h3.5a1 1 0 011 1v1zM4.118 4L4 4.059V13a1 1 0 001 1h6a1 1 0 001-1V4.059L11.882 4H4.118zM2.5 3V2h11v1h-11z"/>
                            </svg>
                            Top Up Cards
                        </button>
                        <button class="btn-outline" onclick="downloadStatement()" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M.5 9.9a.5.5 0 01.5.5v2.5a1 1 0 001 1h12a1 1 0 001-1v-2.5a.5.5 0 011 0v2.5a2 2 0 01-2 2H2a2 2 0 01-2-2v-2.5a.5.5 0 01.5-.5z"/>
                                <path d="M7.646 11.854a.5.5 0 00.708 0l3-3a.5.5 0 00-.708-.708L8.5 10.293V1.5a.5.5 0 00-1 0v8.793L5.354 8.146a.5.5 0 10-.708.708l3 3z"/>
                            </svg>
                            Download Statement
                        </button>
                    </div>
                </div>
            </div>

            <!-- Card Tips -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Security Tips</h3>
                </div>
                <div style="padding: 1.5rem;">
                    <div style="display: flex; flex-direction: column; gap: 1rem;">
                        <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                            <div style="width: 8px; height: 8px; border-radius: 50%; background: #10b981; margin-top: 0.5rem; flex-shrink: 0;"></div>
                            <div>
                                <div style="font-weight: 500; margin-bottom: 0.25rem;">Keep CVV Secret</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">Never share your CVV code with anyone</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                            <div style="width: 8px; height: 8px; border-radius: 50%; background: #10b981; margin-top: 0.5rem; flex-shrink: 0;"></div>
                            <div>
                                <div style="font-weight: 500; margin-bottom: 0.25rem;">Monitor Transactions</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">Check your card activity regularly</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: flex-start; gap: 0.75rem;">
                            <div style="width: 8px; height: 8px; border-radius: 50%; background: #10b981; margin-top: 0.5rem; flex-shrink: 0;"></div>
                            <div>
                                <div style="font-weight: 500; margin-bottom: 0.25rem;">Freeze When Needed</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">Instantly freeze cards if suspicious activity</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Card Modal -->
<div class="modal fade" id="createCardModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Virtual Card</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createCardForm">
                    <div class="mb-3">
                        <label class="form-label">Card Holder Name</label>
                        <input type="text" class="form-control" name="card_holder_name" value="<?php echo htmlspecialchars($_SESSION['first_name'] . ' ' . $_SESSION['last_name']); ?>" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Card Type</label>
                        <select class="form-control" name="card_type" required>
                            <option value="visa">Visa</option>
                            <option value="mastercard">Mastercard</option>
                            <option value="amex">American Express</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Spending Limit</label>
                        <input type="number" class="form-control" name="spending_limit" min="100" max="10000" value="1000" required>
                        <small class="form-text text-muted">Minimum $100, Maximum $10,000</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="createCard()">Create Card</button>
            </div>
        </div>
    </div>
</div>

<script>
function showCreateCardModal() {
    const modal = new bootstrap.Modal(document.getElementById('createCardModal'));
    modal.show();
}

function createCard() {
    const form = document.getElementById('createCardForm');
    const formData = new FormData(form);

    // Show loading state
    const createBtn = event.target;
    const originalText = createBtn.innerHTML;
    createBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Creating...';
    createBtn.disabled = true;

    fetch('ajax/create_card.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Virtual card created successfully!');
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while creating the card');
    })
    .finally(() => {
        createBtn.innerHTML = originalText;
        createBtn.disabled = false;
    });
}

function viewCardDetails(cardId) {
    // Implement card details view
    alert('Card details functionality coming soon!');
}

function topUpCard(cardId) {
    // Implement card top-up
    alert('Card top-up functionality coming soon!');
}

function freezeCard(cardId) {
    // Implement card freeze/unfreeze
    alert('Card freeze functionality coming soon!');
}

function deleteCard(cardId) {
    if (confirm('Are you sure you want to delete this card? This action cannot be undone.')) {
        // Implement card deletion
        alert('Card deletion functionality coming soon!');
    }
}

function showTopUpModal() {
    alert('Top-up modal coming soon!');
}

function downloadStatement() {
    alert('Statement download coming soon!');
}
</script>

<?php
// Include footer
require_once '../includes/dashboard/footer.php';
?>
