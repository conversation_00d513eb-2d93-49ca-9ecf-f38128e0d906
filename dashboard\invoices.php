<?php
require_once '../config/config.php';
requireLogin();

$page_title = 'Invoices & Statements';
$site_name = getBankName();

// Get user data and generate invoice-like statements
try {
    $db = getDB();
    $user_id = $_SESSION['user_id'];

    // Get user account info
    $account_sql = "SELECT * FROM accounts WHERE id = ?";
    $account_result = $db->query($account_sql, [$user_id]);
    $account_data = $account_result->fetch_assoc();

    // Get monthly statements (last 12 months)
    $statements_sql = "SELECT
                         DATE_FORMAT(created_at, '%Y-%m') as statement_month,
                         DATE_FORMAT(created_at, '%M %Y') as month_name,
                         COUNT(*) as transaction_count,
                         SUM(CASE WHEN sender_id = ? THEN amount ELSE 0 END) as total_debits,
                         SUM(CASE WHEN recipient_id = ? THEN amount ELSE 0 END) as total_credits,
                         MIN(created_at) as period_start,
                         MAX(created_at) as period_end
                       FROM transfers
                       WHERE (sender_id = ? OR recipient_id = ?)
                       AND status = 'completed'
                       AND created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
                       GROUP BY DATE_FORMAT(created_at, '%Y-%m')
                       ORDER BY statement_month DESC";

    $statements_result = $db->query($statements_sql, [$user_id, $user_id, $user_id, $user_id]);
    $monthly_statements = [];
    while ($statement = $statements_result->fetch_assoc()) {
        $monthly_statements[] = $statement;
    }

    // Get recent transactions for current month
    $current_month_sql = "SELECT t.*,
                                 CASE
                                     WHEN t.sender_id = ? THEN 'debit'
                                     ELSE 'credit'
                                 END as transaction_type,
                                 CASE
                                     WHEN t.sender_id = ? THEN t.recipient_name
                                     ELSE (SELECT CONCAT(first_name, ' ', last_name) FROM accounts WHERE id = t.sender_id)
                                 END as other_party
                          FROM transfers t
                          WHERE (t.sender_id = ? OR t.recipient_id = ?)
                          AND t.status = 'completed'
                          AND MONTH(t.created_at) = MONTH(CURRENT_DATE())
                          AND YEAR(t.created_at) = YEAR(CURRENT_DATE())
                          ORDER BY t.created_at DESC";

    $current_result = $db->query($current_month_sql, [$user_id, $user_id, $user_id, $user_id]);
    $current_month_transactions = [];
    while ($transaction = $current_result->fetch_assoc()) {
        $current_month_transactions[] = $transaction;
    }

    // Calculate account summary
    $summary_sql = "SELECT
                      COUNT(CASE WHEN sender_id = ? THEN 1 END) as total_debits_count,
                      COUNT(CASE WHEN recipient_id = ? THEN 1 END) as total_credits_count,
                      SUM(CASE WHEN sender_id = ? THEN amount ELSE 0 END) as lifetime_debits,
                      SUM(CASE WHEN recipient_id = ? THEN amount ELSE 0 END) as lifetime_credits
                    FROM transfers
                    WHERE (sender_id = ? OR recipient_id = ?)
                    AND status = 'completed'";

    $summary_result = $db->query($summary_sql, [$user_id, $user_id, $user_id, $user_id, $user_id, $user_id]);
    $account_summary = $summary_result->fetch_assoc();

} catch (Exception $e) {
    error_log("Invoices page error: " . $e->getMessage());
    $account_data = [];
    $monthly_statements = [];
    $current_month_transactions = [];
    $account_summary = ['total_debits_count' => 0, 'total_credits_count' => 0, 'lifetime_debits' => 0, 'lifetime_credits' => 0];
}

// Include header
require_once '../includes/dashboard/header.php';

// Include sidebar
require_once '../includes/dashboard/sidebar.php';
?>

<!-- Main Content -->
<div class="main-content">
    <!-- Top Bar -->
    <div class="top-bar">
        <h1>Invoices & Statements</h1>
        <div class="top-bar-actions">
            <button class="btn-outline" onclick="downloadCurrentStatement()">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M.5 9.9a.5.5 0 01.5.5v2.5a1 1 0 001 1h12a1 1 0 001-1v-2.5a.5.5 0 011 0v2.5a2 2 0 01-2 2H2a2 2 0 01-2-2v-2.5a.5.5 0 01.5-.5z"/>
                    <path d="M7.646 11.854a.5.5 0 00.708 0l3-3a.5.5 0 00-.708-.708L8.5 10.293V1.5a.5.5 0 00-1 0v8.793L5.354 8.146a.5.5 0 10-.708.708l3 3z"/>
                </svg>
                Download Current
            </button>
            <button class="btn-primary" onclick="window.location.reload()">
                <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path fill-rule="evenodd" d="M8 3a5 5 0 104.546 2.914.5.5 0 00-.908-.417A4 4 0 118 4v1z"/>
                    <path d="M8 4.466V.534a.25.25 0 01.41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 018 4.466z"/>
                </svg>
                Refresh
            </button>
            <div style="width: 40px; height: 40px; border-radius: 50%; background: #6366f1; display: flex; align-items: center; justify-content: center; color: white; font-weight: 600;">
                <?php echo strtoupper(substr($_SESSION['first_name'] ?? 'U', 0, 1)); ?>
            </div>
        </div>
    </div>

    <!-- Account Summary -->
    <div class="stats-grid" style="margin-bottom: 2rem;">
        <div class="stat-card">
            <h3 class="stat-title">Total Transactions</h3>
            <p class="stat-value"><?php echo number_format($account_summary['total_debits_count'] + $account_summary['total_credits_count']); ?></p>
            <p class="stat-change">All time</p>
        </div>
        <div class="stat-card">
            <h3 class="stat-title">Total Debits</h3>
            <p class="stat-value">$<?php echo number_format($account_summary['lifetime_debits'], 2); ?></p>
            <p class="stat-change"><?php echo number_format($account_summary['total_debits_count']); ?> transactions</p>
        </div>
        <div class="stat-card">
            <h3 class="stat-title">Total Credits</h3>
            <p class="stat-value">$<?php echo number_format($account_summary['lifetime_credits'], 2); ?></p>
            <p class="stat-change"><?php echo number_format($account_summary['total_credits_count']); ?> transactions</p>
        </div>
        <div class="stat-card">
            <h3 class="stat-title">Current Balance</h3>
            <p class="stat-value">$<?php echo number_format($account_data['balance'] ?? 0, 2); ?></p>
            <p class="stat-change">Available now</p>
        </div>
    </div>

    <!-- Main Dashboard Grid -->
    <div class="dashboard-grid">
        <div class="main-section">
            <!-- Monthly Statements -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Monthly Statements</h3>
                    <button class="btn-outline" onclick="downloadAllStatements()">Download All</button>
                </div>
                <div class="card-body">
                    <?php if (!empty($monthly_statements)): ?>
                        <?php foreach ($monthly_statements as $statement): ?>
                            <div class="transaction-item">
                                <div class="transaction-icon" style="background: #6366f1;">
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                        <path d="M4 0h5.293A1 1 0 0110 .293L13.707 4a1 1 0 01.293.707V14a2 2 0 01-2 2H4a2 2 0 01-2-2V2a2 2 0 012-2zm5.5 1.5v2a1 1 0 001 1h2l-3-3z"/>
                                    </svg>
                                </div>
                                <div class="transaction-content">
                                    <div class="transaction-title">
                                        Statement for <?php echo htmlspecialchars($statement['month_name']); ?>
                                    </div>
                                    <div class="transaction-subtitle">
                                        <?php echo $statement['transaction_count']; ?> transactions •
                                        Debits: $<?php echo number_format($statement['total_debits'], 2); ?> •
                                        Credits: $<?php echo number_format($statement['total_credits'], 2); ?>
                                    </div>
                                </div>
                                <div style="text-align: right;">
                                    <div style="display: flex; gap: 0.5rem; margin-bottom: 0.5rem;">
                                        <button class="btn btn-sm btn-outline" onclick="viewStatement('<?php echo $statement['statement_month']; ?>')">
                                            <svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16">
                                                <path d="M1 2.828c.885-.37 2.154-.769 3.388-.893 1.33-.134 2.458.063 3.112.752v9.746c-.935-.53-2.12-.603-3.213-.493-1.18.12-2.37.461-3.287.811V2.828zm7.5-.141c.654-.689 1.782-.886 3.112-.752 1.234.124 2.503.523 3.388.893v9.923c-.918-.35-2.107-.692-3.287-.81-1.094-.111-2.278-.039-3.213.492V2.687zM8 1.783C7.015.936 5.587.81 4.287.94c-1.514.153-3.042.672-3.994 1.105A.5.5 0 000 2.5v11a.5.5 0 00.707.455c.882-.4 2.303-.881 3.68-1.02 1.409-.142 2.59.087 3.223.877a.5.5 0 00.78 0c.633-.79 1.814-1.019 3.222-.877 1.378.139 2.8.62 3.681 1.02A.5.5 0 0016 13.5v-11a.5.5 0 00-.293-.455c-.952-.433-2.48-.952-3.994-1.105C10.413.809 8.985.936 8 1.783z"/>
                                            </svg>
                                        </button>
                                        <button class="btn btn-sm btn-primary" onclick="downloadStatement('<?php echo $statement['statement_month']; ?>')">
                                            <svg width="14" height="14" fill="currentColor" viewBox="0 0 16 16">
                                                <path d="M.5 9.9a.5.5 0 01.5.5v2.5a1 1 0 001 1h12a1 1 0 001-1v-2.5a.5.5 0 011 0v2.5a2 2 0 01-2 2H2a2 2 0 01-2-2v-2.5a.5.5 0 01.5-.5z"/>
                                                <path d="M7.646 11.854a.5.5 0 00.708 0l3-3a.5.5 0 00-.708-.708L8.5 10.293V1.5a.5.5 0 00-1 0v8.793L5.354 8.146a.5.5 0 10-.708.708l3 3z"/>
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="transaction-date">
                                        <?php echo date('M j', strtotime($statement['period_start'])); ?> - <?php echo date('M j, Y', strtotime($statement['period_end'])); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div style="text-align: center; padding: 3rem 1rem;">
                            <div style="font-size: 3rem; color: #e5e7eb; margin-bottom: 1rem;">📄</div>
                            <h3 style="color: #6b7280; margin-bottom: 0.5rem;">No Statements Available</h3>
                            <p style="color: #9ca3af; margin-bottom: 2rem;">Start making transactions to generate your first statement.</p>
                            <button class="btn-primary" onclick="window.location.href='../transfers/'">Make Your First Transfer</button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Current Month Transactions -->
            <?php if (!empty($current_month_transactions)): ?>
            <div class="card" style="margin-top: 2rem;">
                <div class="card-header">
                    <h3 class="card-title">Current Month Transactions</h3>
                    <span style="font-size: 0.875rem; color: #6b7280;"><?php echo date('F Y'); ?></span>
                </div>
                <div class="card-body">
                    <?php foreach (array_slice($current_month_transactions, 0, 10) as $transaction): ?>
                        <div class="transaction-item">
                            <div class="transaction-icon" style="background: <?php echo $transaction['transaction_type'] === 'debit' ? '#ef4444' : '#10b981'; ?>;">
                                <?php if ($transaction['transaction_type'] === 'debit'): ?>
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                        <path d="M15.854.146a.5.5 0 01.11.54L13.026 8.03A4.5 4.5 0 018 12.5a4.5 4.5 0 115.026-7.47L15.964.686a.5.5 0 01-.11-.54z"/>
                                    </svg>
                                <?php else: ?>
                                    <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                        <path d="M8 15A7 7 0 118 1a7 7 0 010 14zm0 1A8 8 0 108 0a8 8 0 000 16z"/>
                                        <path d="M8 4a.5.5 0 01.5.5v3h3a.5.5 0 010 1h-3v3a.5.5 0 01-1 0v-3h-3a.5.5 0 010-1h3v-3A.5.5 0 018 4z"/>
                                    </svg>
                                <?php endif; ?>
                            </div>
                            <div class="transaction-content">
                                <div class="transaction-title">
                                    <?php echo $transaction['transaction_type'] === 'debit' ? 'Payment to' : 'Payment from'; ?>
                                    <?php echo htmlspecialchars($transaction['other_party']); ?>
                                </div>
                                <div class="transaction-subtitle">
                                    <?php echo htmlspecialchars($transaction['description'] ?? 'No description'); ?>
                                </div>
                            </div>
                            <div style="text-align: right;">
                                <div class="transaction-amount <?php echo $transaction['transaction_type'] === 'debit' ? 'amount-negative' : 'amount-positive'; ?>">
                                    <?php echo $transaction['transaction_type'] === 'debit' ? '-' : '+'; ?>$<?php echo number_format($transaction['amount'], 2); ?>
                                </div>
                                <div class="transaction-date">
                                    <?php echo date('M j, g:i A', strtotime($transaction['created_at'])); ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    <?php if (count($current_month_transactions) > 10): ?>
                        <div style="text-align: center; margin-top: 1rem;">
                            <a href="../transfers/history.php" style="color: #6366f1; text-decoration: none;">View All <?php echo count($current_month_transactions); ?> Transactions</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar Section -->
        <div class="sidebar-section">
            <!-- Account Information -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Account Details</h3>
                </div>
                <div style="padding: 1.5rem;">
                    <div style="display: flex; flex-direction: column; gap: 1rem;">
                        <div>
                            <div style="font-size: 0.875rem; color: #6b7280; margin-bottom: 0.25rem;">Account Holder</div>
                            <div style="font-weight: 600;"><?php echo htmlspecialchars(($account_data['first_name'] ?? '') . ' ' . ($account_data['last_name'] ?? '')); ?></div>
                        </div>
                        <div>
                            <div style="font-size: 0.875rem; color: #6b7280; margin-bottom: 0.25rem;">Account Number</div>
                            <div style="font-weight: 600; font-family: monospace;"><?php echo htmlspecialchars($account_data['account_number'] ?? ''); ?></div>
                        </div>
                        <div>
                            <div style="font-size: 0.875rem; color: #6b7280; margin-bottom: 0.25rem;">Account Type</div>
                            <div style="font-weight: 600; text-transform: capitalize;"><?php echo htmlspecialchars($account_data['account_type'] ?? ''); ?></div>
                        </div>
                        <div>
                            <div style="font-size: 0.875rem; color: #6b7280; margin-bottom: 0.25rem;">Member Since</div>
                            <div style="font-weight: 600;"><?php echo date('F Y', strtotime($account_data['created_at'] ?? '')); ?></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Download Options -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Download Options</h3>
                </div>
                <div style="padding: 1.5rem;">
                    <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                        <button class="btn-primary" onclick="downloadCurrentStatement()" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M.5 9.9a.5.5 0 01.5.5v2.5a1 1 0 001 1h12a1 1 0 001-1v-2.5a.5.5 0 011 0v2.5a2 2 0 01-2 2H2a2 2 0 01-2-2v-2.5a.5.5 0 01.5-.5z"/>
                                <path d="M7.646 11.854a.5.5 0 00.708 0l3-3a.5.5 0 00-.708-.708L8.5 10.293V1.5a.5.5 0 00-1 0v8.793L5.354 8.146a.5.5 0 10-.708.708l3 3z"/>
                            </svg>
                            Current Month
                        </button>
                        <button class="btn-outline" onclick="downloadYearlyStatement()" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path d="M4 0h5.293A1 1 0 0110 .293L13.707 4a1 1 0 01.293.707V14a2 2 0 01-2 2H4a2 2 0 01-2-2V2a2 2 0 012-2zm5.5 1.5v2a1 1 0 001 1h2l-3-3z"/>
                            </svg>
                            Yearly Summary
                        </button>
                        <button class="btn-outline" onclick="downloadTaxStatement()" style="width: 100%;">
                            <svg width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right: 0.5rem;">
                                <path fill-rule="evenodd" d="M1.5 1.5A.5.5 0 012 1h12a.5.5 0 01.5.5v2a.5.5 0 01-.128.334L10 8.692V13.5a.5.5 0 01-.342.474l-3 1A.5.5 0 016 14.5V8.692L1.628 3.834A.5.5 0 011.5 3.5v-2z"/>
                            </svg>
                            Tax Statement
                        </button>
                    </div>
                </div>
            </div>

            <!-- Statement Formats -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Available Formats</h3>
                </div>
                <div style="padding: 1rem;">
                    <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                        <div style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem; border: 1px solid #e5e7eb; border-radius: 8px;">
                            <div style="width: 32px; height: 32px; border-radius: 50%; background: #ef4444; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.75rem; font-weight: 600;">
                                PDF
                            </div>
                            <div>
                                <div style="font-weight: 500; margin-bottom: 0.25rem;">PDF Format</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">Standard format for printing</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem; border: 1px solid #e5e7eb; border-radius: 8px;">
                            <div style="width: 32px; height: 32px; border-radius: 50%; background: #10b981; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.75rem; font-weight: 600;">
                                CSV
                            </div>
                            <div>
                                <div style="font-weight: 500; margin-bottom: 0.25rem;">CSV Format</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">For spreadsheet analysis</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem; border: 1px solid #e5e7eb; border-radius: 8px;">
                            <div style="width: 32px; height: 32px; border-radius: 50%; background: #6366f1; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.75rem; font-weight: 600;">
                                XLS
                            </div>
                            <div>
                                <div style="font-weight: 500; margin-bottom: 0.25rem;">Excel Format</div>
                                <div style="font-size: 0.875rem; color: #6b7280;">For detailed analysis</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function viewStatement(month) {
    alert(`Viewing statement for ${month}. Full statement viewer coming soon!`);
}

function downloadStatement(month) {
    alert(`Downloading statement for ${month}. Download functionality coming soon!`);
}

function downloadCurrentStatement() {
    alert('Downloading current month statement. Download functionality coming soon!');
}

function downloadAllStatements() {
    alert('Downloading all statements. Bulk download functionality coming soon!');
}

function downloadYearlyStatement() {
    alert('Downloading yearly summary. Download functionality coming soon!');
}

function downloadTaxStatement() {
    alert('Downloading tax statement. Tax document generation coming soon!');
}
</script>

<?php
// Include footer
require_once '../includes/dashboard/footer.php';
?>
